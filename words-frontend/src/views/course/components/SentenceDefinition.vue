<template>
  <div class="stage-container">
    <div class="stage-header">
      <div class="stage-title">{{ (stepIndex || 0) + 1 }}.【句子理解】（1）分析结构，理解释义（2）听两种发音（3）大声模仿跟读</div>
    </div>

    <div class="stage-content">
      <div class="word-display">
        <div class="word-display-text">{{ currentStepInfo?.wordInfo?.sentences.sentenceEn }}</div>
      </div>
      <div class="sentence-video-text">
        <span class="pronunciation uk-pronunciation" @click="playAudioUtil(currentStepInfo?.wordInfo?.sentences?.audioUkUrl)">英式<el-icon><VideoPlay /></el-icon></span>
        <span class="pronunciation us-pronunciation" @click="playAudioUtil(currentStepInfo?.wordInfo?.sentences?.audioUsUrl)" style="margin-left: 10px">美式<el-icon><VideoPlay /></el-icon></span>
      </div>
      <div class="definition-card">
        <div class="sentence-section">
          <div class="translation">
            <span class="label">翻译:</span>
            <el-icon
              class="eye-icon"
              @click="toggleTranslationVisible"
              :title="translationVisible ? '隐藏翻译' : '显示翻译'"
            >
              <View v-if="translationVisible" />
              <Hide v-else />
            </el-icon>
            <span v-if="translationVisible" class="translation-content">
              {{ currentStepInfo?.wordInfo?.sentences.sentenceCn }}
            </span>
          </div>
          <div class="imitation-section" @click="showImitationVideo">
            <el-icon class="imitation-icon"><VideoCamera /></el-icon>
            <div class="imitation-text">
              <div class="imitation-title">我是小老师超级模仿秀</div>
              <div class="imitation-subtitle">请尝试模仿筱灵老师的讲解方法，把这个句子讲给你的老师听！</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 视频对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="小老师超级模仿秀"
      width="70%"
      :before-close="handleClose"
      class="imitation-dialog"
    >
      <video 
        v-if="dialogVisible"
        :src="currentStepInfo?.wordInfo?.videoUrl"
        controls 
        class="imitation-video"
      ></video>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import {ref, onMounted, onUnmounted} from 'vue'
import {COURSE_sessionStorage_INFO_KEY, CurrentStepInfo, getStepInfoByType, submitCourseStepApi} from "@/api/course";
import {playAudioUtil, useAudioPreloader, useVideoPreloader} from '@/api/course/util'
import {VideoCamera, VideoPlay, View, Hide} from "@element-plus/icons-vue";
import {ElMessageBox} from "element-plus";

const props = defineProps({
  selectWordText: String,
  courseId: {
    type: [String, Number],
    required: true
  },
  stepIndex: Number,
  sectionId: String
})

const emit = defineEmits(['complete'])
const currentStepInfo = ref<CurrentStepInfo | null>(null);
const dialogVisible = ref(false)
const translationVisible = ref(false) // 控制翻译内容显示/隐藏，默认隐藏

// 音频预加载相关
let audioPreloader: ReturnType<typeof useAudioPreloader> | null = null;

// 视频预加载相关
let videoPreloader: ReturnType<typeof useVideoPreloader> | null = null;

// 切换翻译内容显示/隐藏
const toggleTranslationVisible = () => {
  translationVisible.value = !translationVisible.value
}

// 显示视频对话框
const showImitationVideo = () => {
  let videoUrl = currentStepInfo.value?.wordInfo?.videoUrl
  if(!videoUrl){
    ElMessageBox.alert("暂无视频");
    return
  }
  dialogVisible.value = true
}

// 关闭对话框前的处理
const handleClose = () => {
  dialogVisible.value = false
}
/**
 * 提交课程步骤
 */
const submitCourseStep = () => {
  // 如果是已完成状态，不用重复提交
  let status = currentStepInfo.value.step.status
  if (status === "已完成") {
    return;
  }
  let wordId = currentStepInfo.value.wordId

  let submitResult = {
    stepId: currentStepInfo.value.step.id,
    result: "正确",
    studentAnswer: "正确"
  }
  submitCourseStepApi(props.courseId, props.sectionId, wordId, submitResult, true)
}

const playAudio = (url?: string) => {
  playAudioUtil(url)
}

onMounted(async () => {
  // 先从sessionStorage获取
  let obj = sessionStorage.getItem(COURSE_sessionStorage_INFO_KEY + props.courseId)
  if (obj !== null) {
    try {
      let courseInfo = JSON.parse(obj);
      currentStepInfo.value = await getStepInfoByType(courseInfo, '句子翻译讲解', props.selectWordText)
      
      // 预加载音频文件
      if (currentStepInfo.value?.wordInfo?.sentences) {
        const audioUrls = [
          currentStepInfo.value.wordInfo.sentences.audioUkUrl,
          currentStepInfo.value.wordInfo.sentences.audioUsUrl
        ].filter(url => url && url.trim() !== '');
        
        if (audioUrls.length > 0) {
          console.log('开始预加载句子音频文件:', audioUrls);
          audioPreloader = useAudioPreloader(audioUrls);
          await audioPreloader.preloadAudios();
          console.log('句子音频预加载完成');
        }
      }
      
      // 预加载视频文件
      if (currentStepInfo.value?.wordInfo?.videoUrl) {
        const videoUrls = [currentStepInfo.value.wordInfo.videoUrl].filter(url => url && url.trim() !== '');
        
        if (videoUrls.length > 0) {
          console.log('开始预加载视频文件:', videoUrls);
          videoPreloader = useVideoPreloader(videoUrls);
          await videoPreloader.preloadVideos();
          console.log('视频预加载完成');
        }
      }
      
      return;
    } catch (e) {
      console.error("Error parsing sessionStorage data:", e);
    }
  }
})

// 组件卸载时清除缓存以释放内存
onUnmounted(() => {
  if (audioPreloader) {
    audioPreloader.clearCache();
    console.log('音频缓存已清除');
  }
  if (videoPreloader) {
    videoPreloader.clearCache();
    console.log('视频缓存已清除');
  }
})

defineExpose({
  submitCourseStep
})
</script>

<style scoped>
.stage-container {
  width: 100%;
  height: 100%;
  flex-direction: column;
  align-items: center;
  padding: 40px;
  /* 默认不显示滚动条，只在内容溢出时才显示 */
  overflow-y: auto;
}

.stage-header {
  width: 100%;
  margin-bottom: 20px;
}

.stage-title {
  font-size: 24px;
  font-weight: bold;
  color: #5d4037;
}

.definition-card {
  width: 100%;
  padding: 20px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-top: 20px;
}

.stage-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  min-height: 0;
}

.word-display {
  font-size: 40px;
  font-weight: bold;
  color: #5d4037;
}

.word-display-text {
  font-family: "Comic Sans MS", cursive, sans-serif;
  margin-right: 40px;
}

.sentence-section {
  //margin-top: 15px;
}

.translation {
  font-size: 26px;
  color: #666;
  margin-bottom: 15px;
  line-height: 1.6;
}

.pronunciation span {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: #666;
}

.pronunciation .el-icon {
  margin-left: 5px;
  color: #FF9800;
}

.label {
  font-weight: bold;
  color: #FF9800;
  margin-right: 8px;
}

.eye-icon {
  margin-left: 8px;
  margin-right: 8px;
  cursor: pointer;
  color: #FF9800;
  font-size: 20px;
  transition: all 0.3s;
}

.eye-icon:hover {
  color: #F57C00;
  transform: scale(1.1);
}

.translation-content {
  color: #666;
}

.sentence-video-text {
  font-size: 26px;
  font-weight: bold;
  margin: 10px 0 15px 0;
  display: inline-flex !important;
  align-items: center !important;
  cursor: pointer;
  font-family: "Comic Sans MS", cursive, sans-serif;
}


/* 模仿秀样式 */
.imitation-section {
  display: flex;
  align-items: center;
  padding: 15px;
  background-color: #fff8e1;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  margin-top: 20px;
  border: 1px solid #ffe082;
}

.imitation-section:hover {
  background-color: #ffecb3;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 193, 7, 0.2);
}

.imitation-icon {
  font-size: 28px;
  color: #ff9800;
  margin-right: 15px;
}

.imitation-text {
  flex: 1;
}

.imitation-title {
  font-size: 18px;
  color: #ff6f00;
  font-weight: bold;
  margin-bottom: 5px;
}

.imitation-subtitle {
  font-size: 14px;
  color: #5d4037;
}

/* 视频对话框样式 */
.imitation-dialog :deep(.el-dialog__header) {
  background-color: #ff9800;
  padding: 15px 20px;
}

.imitation-dialog :deep(.el-dialog__title) {
  color: white;
  font-weight: bold;
}

.imitation-dialog :deep(.el-dialog__body) {
  padding: 0;
  background-color: #000;
}

.imitation-video {
  width: 100%;
  max-height: 70vh;
  display: block;
}

/* 动画关键帧 */
@keyframes bounce {
  0%,
  100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-10px);
  }
}

/* 响应式设计 - 屏幕宽度 */
@media screen and (max-width: 768px) {
  .stage-container {
    padding: 20px 15px;
  }
  
  .stage-title {
    font-size: 20px;
  }
  
  .definition-card {
    padding: 15px;
    margin-top: 15px;
  }
  
  .word-display {
    font-size: 32px;
  }
  
  .word-display-text {
    margin-right: 20px;
  }
  
  .translation {
    font-size: 22px;
  }

  .eye-icon {
    font-size: 18px;
  }

  .sentence-video-text {
    font-size: 18px;
  }
  
  .imitation-section {
    padding: 12px;
  }
  
  .imitation-icon {
    font-size: 24px;
    margin-right: 12px;
  }
  
  .imitation-title {
    font-size: 16px;
  }
  
  .imitation-subtitle {
    font-size: 13px;
  }
  
  .imitation-dialog :deep(.el-dialog__header) {
    padding: 12px 15px;
  }
}

@media screen and (max-width: 480px) {
  .stage-container {
    padding: 15px 10px;
  }
  
  .stage-title {
    font-size: 18px;
  }
  
  .definition-card {
    padding: 12px;
    margin-top: 10px;
  }
  
  .word-display {
    font-size: 26px;
  }
  
  .word-display-text {
    margin-right: 10px;
  }
  
  .translation {
    font-size: 18px;
  }

  .eye-icon {
    font-size: 16px;
  }

  .sentence-video-text {
    font-size: 16px;
  }
  
  .imitation-section {
    padding: 10px;
    flex-direction: column;
    align-items: flex-start;
  }
  
  .imitation-icon {
    font-size: 22px;
    margin-right: 0;
    margin-bottom: 8px;
  }
  
  .imitation-title {
    font-size: 15px;
  }
  
  .imitation-subtitle {
    font-size: 12px;
  }
  
  .imitation-dialog :deep(.el-dialog__header) {
    padding: 10px;
  }
  
  .imitation-dialog :deep(.el-dialog) {
    width: 90% !important;
  }
}

/* 响应式设计 - 屏幕高度 */
@media screen and (max-height: 700px) {
  .stage-container {
    padding: 15px 10px;
  }
  
  .stage-header {
    margin-bottom: 10px;
  }
  
  .stage-title {
    font-size: 18px;
  }
  
  .definition-card {
    padding: 15px;
    margin-top: 10px;
  }
  
  .word-display {
    font-size: 32px;
  }
  
  .word-display-text {
    margin-right: 20px;
  }
  
  .sentence-video-text {
    margin: 5px 0 10px 0;
  }
  
  .translation {
    font-size: 22px;
    margin-bottom: 10px;
  }

  .eye-icon {
    font-size: 18px;
  }
  
  .imitation-section {
    padding: 12px;
    margin-top: 10px;
  }
  
  .imitation-dialog :deep(.el-dialog__header) {
    padding: 12px;
  }
  
  .imitation-video {
    max-height: 60vh;
  }
}

@media screen and (max-height: 500px) {
  .stage-container {
    padding: 10px 8px;
  }
  
  .stage-header {
    margin-bottom: 5px;
  }
  
  .stage-title {
    font-size: 16px;
  }
  
  .definition-card {
    padding: 10px;
    margin-top: 5px;
  }
  
  .word-display {
    font-size: 24px;
  }
  
  .word-display-text {
    margin-right: 10px;
  }
  
  .sentence-video-text {
    margin: 3px 0 5px 0;
    font-size: 14px;
  }
  
  .translation {
    font-size: 16px;
    margin-bottom: 5px;
  }

  .eye-icon {
    font-size: 14px;
  }
  
  .imitation-section {
    padding: 8px;
    margin-top: 5px;
  }
  
  .imitation-icon {
    font-size: 20px;
    margin-right: 8px;
  }
  
  .imitation-title {
    font-size: 14px;
    margin-bottom: 2px;
  }
  
  .imitation-subtitle {
    font-size: 12px;
  }
  
  .imitation-dialog :deep(.el-dialog__header) {
    padding: 8px;
  }
  
  .imitation-dialog :deep(.el-dialog__title) {
    font-size: 14px;
  }
  
  .imitation-video {
    max-height: 50vh;
  }
}

/* 1080p 150%缩放优化 */
@media screen and (max-height: 720px) and (max-width: 1280px) {
  .stage-container {
    padding: 12px 8px;
    font-size: 13px;
  }
  
  .stage-title {
    font-size: 14px !important;
  }
  
  .definition-card {
    padding: 16px;
    margin-top: 12px;
  }
  
  .word-display {
    font-size: 28px !important;
  }
  
  .word-display-text {
    margin-right: 22px;
  }
  
  .sentence-video-text {
    margin: 6px 0 12px 0;
    font-size: 17px;
  }
  
  .translation {
    font-size: 20px !important;
    margin-bottom: 12px;
  }
  
  .eye-icon {
    font-size: 18px;
  }
  
  .imitation-section {
    padding: 14px;
    margin-top: 12px;
  }
  .imitation-title {
    font-size: 16px;
  }
  .imitation-subtitle{
    font-size: 12px;
  }
}

/* 极小屏幕高度 - 进一步缩放避免滚动条 */
@media screen and (max-height: 450px) {
  .stage-container {
    padding: 8px 5px !important;
    font-size: 10px;
  }
  
  .stage-title {
    font-size: 14px !important;
  }
  
  .definition-card {
    padding: 8px !important;
    margin-top: 5px !important;
  }
  
  .word-display {
    font-size: 20px !important;
  }
  
  .word-display-text {
    margin-right: 8px !important;
  }
  
  .sentence-video-text {
    margin: 3px 0 5px 0 !important;
    font-size: 12px !important;
  }
  
  .translation {
    font-size: 14px !important;
    margin-bottom: 5px !important;
  }
}

/* 超小屏幕高度 - 最大程度缩放 */
@media screen and (max-height: 380px) {
  .stage-container {
    padding: 5px 3px !important;
    font-size: 9px;
  }
  
  .stage-title {
    font-size: 12px !important;
  }
  
  .definition-card {
    padding: 6px !important;
    margin-top: 3px !important;
  }
  
  .word-display {
    font-size: 18px !important;
  }
  
  .sentence-video-text {
    font-size: 11px !important;
    margin: 2px 0 4px 0 !important;
  }
  
  .translation {
    font-size: 12px !important;
    margin-bottom: 3px !important;
  }
}

/* 极限小屏幕高度 - 最小可用尺寸 */
@media screen and (max-height: 320px) {
  .stage-container {
    padding: 3px 2px !important;
    font-size: 8px;
  }
  
  .stage-title {
    font-size: 10px !important;
  }
  
  .definition-card {
    padding: 4px !important;
    margin-top: 2px !important;
  }
  
  .word-display {
    font-size: 15px !important;
  }
  
  .sentence-video-text {
    font-size: 9px !important;
    margin: 1px 0 2px 0 !important;
  }
  
  .translation {
    font-size: 10px !important;
    margin-bottom: 2px !important;
  }

  .eye-icon {
    font-size: 12px;
  }
  
  .imitation-section {
    padding: 6px;
    margin-top: 3px;
  }
  
  .imitation-icon {
    font-size: 18px;
    margin-right: 6px;
  }
  
  .imitation-title {
    font-size: 12px;
    margin-bottom: 1px;
  }
  
  .imitation-subtitle {
    font-size: 10px;
  }
  
  .imitation-dialog :deep(.el-dialog) {
    width: 95% !important;
  }
  
  .imitation-video {
    max-height: 45vh;
  }
}

.pronunciation:hover {
  color: #FF9800;
  transform: translateY(-2px);
}
</style>